//
// Created by <PERSON><PERSON><PERSON> on 2025/8/6.
//


#include "PythonEngine.h"

#include <filesystem>
#include <fstream>
#include <iostream>
#include <Python.h>
#include <json.hpp>
#include <optional>


using json = nlohmann::json;
namespace fs = std::filesystem;


// RAII 守卫类用于自动恢复工作目录
class CurrentPathGuard {
public:
    explicit CurrentPathGuard(const fs::path& target_dir)
        : original_path_(fs::current_path()) {
        if (!target_dir.empty()) {
            fs::current_path(target_dir);
        }
    }

    ~CurrentPathGuard() {
        try {
            fs::current_path(original_path_);
        } catch (...) {
            // 恢复失败时记录错误
            std::cerr << "Warning: Failed to restore working directory" << std::endl;
        }
    }

private:
    fs::path original_path_;
};

// 辅助函数：获取Python标准库路径
std::string getPythonStdlibPath() {
    PyObject* sysPath = PySys_GetObject("path");
    if (!sysPath) {
        return "";
    }

    Py_ssize_t size = PyList_Size(sysPath);
    for (Py_ssize_t i = 0; i < size; i++) {
        PyObject* item = PyList_GetItem(sysPath, i);
        const char* path = PyUnicode_AsUTF8(item);
        if (std::string(path).find("lib/python") != std::string::npos) {
            return path;
        }
    }
    return "";
}

PythonEngine::PythonEngine() : is_initialized_(false) {
    // if (!Py_IsInitialized()) {
    //     Py_Initialize();
    //     PyEval_InitThreads();
    //     PyEval_SaveThread(); // Release GIL
    // }

    // 设置Python路径（关键修复）
    const char* pythonHome = std::getenv("PYTHONHOME");
    if (!pythonHome) {
        // 尝试自动检测Python安装路径
        PyStatus status;
        PyConfig config;
        PyConfig_InitPythonConfig(&config);

        // 设置程序名称
        status = PyConfig_SetString(&config, &config.program_name, L"multi_engine");
        if (PyStatus_Exception(status)) {
            PyConfig_Clear(&config);
            return;
        }

        // 初始化Python
        status = Py_InitializeFromConfig(&config);
        PyConfig_Clear(&config);
        if (PyStatus_Exception(status)) {
            std::cerr << "Python initialization failed: "
                      << status.err_msg << std::endl;
            return;
        }
    } else {
        // 使用环境变量设置的路径
        Py_SetPythonHome(Py_DecodeLocale(pythonHome, nullptr));
        Py_Initialize();
    }

    PyEval_InitThreads();
    PyEval_SaveThread(); // 释放GIL

    is_initialized_ = true;
}

PythonEngine::~PythonEngine() {
    // Don't call Py_Finalize here to allow other Python interactions
}

bool PythonEngine::initialize() {
    return is_initialized_;
}

std::string PythonEngine::execute(const std::string& script) {
    // 确保线程安全
    std::lock_guard<std::mutex> lock(mutex_);

    if (!is_initialized_) {
        return "Python engine not initialized";
    }

    // 获取 GIL
    PyGILState_STATE gstate = PyGILState_Ensure();

    // 1. 设置 C++ 工作目录 (RAII 模式)
    std::optional<CurrentPathGuard> path_guard;
    if (!workingDirectory_.empty()) {
        try {
            path_guard.emplace(workingDirectory_);
        } catch (const std::exception& e) {
            PyGILState_Release(gstate);
            return "Failed to set working directory: " + std::string(e.what());
        }
    }

    // 2. 添加额外路径到 sys.path
    for (const auto& path : additionalPaths_) {
        // 使用原始字符串字面量处理特殊字符
        std::string sanitized_path = path.string();
        // 转义单引号
        size_t pos = 0;
        while ((pos = sanitized_path.find('\'', pos)) != std::string::npos) {
            sanitized_path.replace(pos, 1, "\\'");
            pos += 2;
        }

        std::string cmd =
            "import sys\n"
            "path = r'" + sanitized_path + "'\n"
            "if path not in sys.path:\n"
            "    sys.path.insert(0, path)\n";

        PyRun_SimpleString(cmd.c_str());
    }

    // 3. 设置 Python 工作目录
    if (!workingDirectory_.empty()) {
        std::string sanitized_path = workingDirectory_.string();
        size_t pos = 0;
        while ((pos = sanitized_path.find('\'', pos)) != std::string::npos) {
            sanitized_path.replace(pos, 1, "\\'");
            pos += 2;
        }

        std::string chdir_cmd =
            "import os\n"
            "try:\n"
            "    os.chdir(r'" + sanitized_path + "')\n"
            "except Exception as e:\n"
            "    print(f\"[PythonEngine] Directory change error: {e}\")\n";

        PyRun_SimpleString(chdir_cmd.c_str());
    }

    // 4. 准备执行环境
    PyObject* main_module = PyImport_AddModule("__main__");
    if (!main_module) {
        PyGILState_Release(gstate);
        return "Error: cannot get __main__ module";
    }

    PyObject* global_dict = PyModule_GetDict(main_module);
    PyObject* local_dict = PyDict_New();
    if (!local_dict) {
        PyGILState_Release(gstate);
        return "Error: cannot create local dictionary";
    }

    // 5. 执行用户脚本
    std::string result;
    PyObject* py_result = PyRun_String(script.c_str(), Py_file_input, global_dict, local_dict);

    // 6. 处理执行结果
    if (py_result) {
        // 检查是否设置了 __engine_result 变量
        PyObject* engine_result = PyDict_GetItemString(local_dict, "__engine_result");
        if (engine_result) {
            PyObject* str_repr = PyObject_Str(engine_result);
            if (str_repr) {
                const char* c_str = PyUnicode_AsUTF8(str_repr);
                result = c_str ? c_str : "Output conversion failed";
                Py_DECREF(str_repr);
            } else {
                result = "No __engine_result value";
            }
        } else {
            // 没有指定 __engine_result，返回整个本地字典的字符串表示
            PyObject* str_repr = PyObject_Str(local_dict);
            if (str_repr) {
                const char* c_str = PyUnicode_AsUTF8(str_repr);
                result = c_str ? c_str : "Output conversion failed";
                Py_DECREF(str_repr);
            } else {
                result = "No output generated";
            }
        }
        Py_DECREF(py_result);
    } else {
        // 执行出错 - 获取错误信息
        PyObject *type = nullptr, *value = nullptr, *traceback = nullptr;
        PyErr_Fetch(&type, &value, &traceback);
        PyErr_NormalizeException(&type, &value, &traceback);

        if (value) {
            PyObject* str_value = PyObject_Str(value);
            if (str_value) {
                const char* c_str = PyUnicode_AsUTF8(str_value);
                result = "Python error: " + std::string(c_str ? c_str : "unknown");
                Py_DECREF(str_value);
            } else {
                result = "Python error: unknown (no error message)";
            }
        } else {
            result = "Python error: unknown (no error value)";
        }

        // 打印完整的错误回溯
        if (traceback) {
            PyObject* module_name = PyUnicode_FromString("traceback");
            PyObject* traceback_module = PyImport_Import(module_name);
            Py_XDECREF(module_name);

            if (traceback_module) {
                PyObject* format_exception = PyObject_GetAttrString(traceback_module, "format_exception");
                if (format_exception && PyCallable_Check(format_exception)) {
                    PyObject* formatted = PyObject_CallFunctionObjArgs(format_exception, type, value, traceback, nullptr);
                    if (formatted) {
                        PyObject* joined = PyUnicode_Join(PyUnicode_FromString(""), formatted);
                        if (joined) {
                            const char* traceback_str = PyUnicode_AsUTF8(joined);
                            if (traceback_str) {
                                result += "\nTraceback:\n" + std::string(traceback_str);
                            }
                            Py_DECREF(joined);
                        }
                        Py_DECREF(formatted);
                    }
                    Py_DECREF(format_exception);
                }
                Py_DECREF(traceback_module);
            }
        }

        Py_XDECREF(type);
        Py_XDECREF(value);
        Py_XDECREF(traceback);
        PyErr_Clear();  // 确保错误状态被清除
    }

    // 7. 清理资源
    Py_DECREF(local_dict);
    PyGILState_Release(gstate);

    return result;
}

void PythonEngine::loadConfiguration(const std::string& configPath)
{
    std::ifstream f(configPath);
    if (!f.is_open()) {
        throw std::runtime_error("Cannot open configuration file: " + configPath);
    }

    json config = json::parse(f);

    // 添加所有Python路径
    for (const auto& path : config["python_paths"]) {
        addPythonPath(path.get<std::string>());
    }

    // 缓存依赖信息
    for (const auto& algo : config["algorithms"]) {
        AlgorithmInfo info;
        info.engine = algo["engine"].get<std::string>();
        info.module = algo["module"].get<std::string>();
        info.function = algo["function"].get<std::string>();

        if (algo.contains("dependencies")) {
            for (const auto& dep : algo["dependencies"]) {
                info.dependencies.push_back(dep.get<std::string>());
            }
        }

        algorithmRegistry_[algo["name"].get<std::string>()] = info;
    }
}

std::string PythonEngine::executeAlgorithm(const std::string& algoName, const std::vector<std::string>& args)
{
    // 查找算法
    auto it = algorithmRegistry_.find(algoName);
    if (it == algorithmRegistry_.end()) {
        return "Error: Algorithm '" + algoName + "' not registered";
    }


    const AlgorithmInfo& algoInfo = it->second;

    // 确保依赖已加载
    ensureDependenciesLoaded(algoInfo);


    // 构建调用脚本
    std::string script = "from " + algoInfo.module + " import " + algoInfo.function + "\n"
                         "__engine_result = " + algoInfo.function + "(";

    for (size_t i = 0; i < args.size(); ++i) {
        if (i > 0) script += ", ";
        script += args[i];
    }
    script += ")";


    // 执行脚本
    return execute(script);
}

// 添加 ensureDependenciesLoaded 方法
void PythonEngine::ensureDependenciesLoaded(const AlgorithmInfo& algoInfo) {
    PyGILState_STATE gstate = PyGILState_Ensure();

    // 导入所有依赖
    for (const auto& dep : algoInfo.dependencies) {
        std::string importCmd = "try:\n"
                                "    import " + dep + "\n"
                                "except ImportError:\n"
                                "    pass\n";  // 静默失败，依赖可能已在其他地方导入
        PyRun_SimpleString(importCmd.c_str());
    }

    PyGILState_Release(gstate);
}

void PythonEngine::addPythonPath(const std::string& path)
{
    std::lock_guard<std::mutex> lock(mutex_);

    fs::path absPath = fs::absolute(path);
    if (!fs::exists(absPath)) {
        std::cerr << "Warning: Python path does not exist: " << absPath << std::endl;
        return;
    }

    // 避免重复添加
    if (std::find(additionalPaths_.begin(), additionalPaths_.end(), absPath) != additionalPaths_.end()) {
        return;
    }

    additionalPaths_.push_back(absPath);

    if (is_initialized_) {
        PyGILState_STATE gstate = PyGILState_Ensure();
        std::string cmd = "import sys\n"
                          "if r'" + absPath.string() + "' not in sys.path:\n"
                          "    sys.path.insert(0, r'" + absPath.string() + "')\n";
        PyRun_SimpleString(cmd.c_str());
        PyGILState_Release(gstate);
    }
}
