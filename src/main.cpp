#include "SchedulerEngine.h"
#include "EngineManager.h"
#include "PythonEngine.h"
#include "MatlabEngine.h"
#include <iostream>

int main() {
    try
    {
        // 初始化引擎
        EngineManager::instance().registerEngine("python", std::make_unique<PythonEngine>());
        // EngineManager::instance().registerEngine("matlab", std::make_unique<MatlabEngine>());


        // 获取Python引擎并加载配置
        auto* pythonEngine = dynamic_cast<PythonEngine*>(
            EngineManager::instance().getEngine("python")
        );


        if (pythonEngine) {
            pythonEngine->loadConfiguration("config/algorithms_config.json");
        }

        // 配置调度引擎
        SchedulerEngine engine;
        engine.loadConfiguration("config/algorithms_config.json");

        std::string result = engine.compute("matrix_multiply({10, 20}, {30,40})");
        std::cout << "Matrix multiplication result: " << result << std::endl;

        // result = engine.compute("fft_analysis([0.1, 0.5, 0.3, 0.8])");
        // std::cout << "FFT analysis result: " << result << std::endl;
    }catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    return 0;
}