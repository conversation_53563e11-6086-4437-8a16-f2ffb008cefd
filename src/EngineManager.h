//
// Created by <PERSON><PERSON><PERSON> on 2025/8/6.
//

#ifndef ENGINEMANAGER_H
#define ENGINEMANAGER_H

#include "EngineBase.h"
#include <memory>
#include <unordered_map>
#include <mutex>
#include <string>

class EngineManager {
public:
    static EngineManager& instance();

    void registerEngine(const std::string& name, std::unique_ptr<EngineBase> engine);
    EngineBase* getEngine(const std::string& name);

    EngineManager(const EngineManager&) = delete;
    EngineManager& operator=(const EngineManager&) = delete;

private:
    EngineManager() = default;
    std::mutex mtx_;
    std::unordered_map<std::string, std::unique_ptr<EngineBase>> engines_;
};
#endif //ENGINEMANAGER_H
