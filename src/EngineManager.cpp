//
// Created by ma<PERSON><PERSON> on 2025/8/6.
//
#include "EngineManager.h"

EngineManager& EngineManager::instance() {
    static EngineManager instance;
    return instance;
}

void EngineManager::registerEngine(const std::string& name, std::unique_ptr<EngineBase> engine) {
    std::lock_guard<std::mutex> lock(mtx_);
    engines_[name] = std::move(engine);
}

EngineBase* EngineManager::getEngine(const std::string& name) {
    std::lock_guard<std::mutex> lock(mtx_);
    auto it = engines_.find(name);
    return (it != engines_.end()) ? it->second.get() : nullptr;
}