// SchedulerEngine.cpp
#include "SchedulerEngine.h"

#include <fstream>

#include "json.hpp"

SchedulerEngine::SchedulerEngine() {
    // 注册默认数学函数
    executor_.registerFunction("sqrt", "python", "math.sqrt");
    executor_.registerFunction("sin", "matlab", "sin");
    // 可以添加更多默认函数...
}

void SchedulerEngine::loadConfiguration(const std::string& configPath)
{
    std::ifstream f(configPath);
    if (!f.is_open()) {
        throw std::runtime_error("Cannot open configuration file: " + configPath);
    }

    nlohmann::json config = nlohmann::json::parse(f);

    for (const auto& algo : config["algorithms"]) {
        registerAlgorithm(
            algo["name"].get<std::string>(),
            algo["engine"].get<std::string>(),
            algo["module"].get<std::string>(),
            algo["function"].get<std::string>()
        );
    }
}

void SchedulerEngine::registerAlgorithm(const std::string& name,
                         const std::string& engine,
                         const std::string& module,
                         const std::string& function) {
    // executor_.registerFunction(name, engine, backendFunc);
    // 格式化为引擎需要的调用字符串
    std::string backendFunc = module + "." + function;
    executor_.registerFunction(name, engine, backendFunc);
}

std::string SchedulerEngine::compute(const std::string& expression) {
    return executor_.execute(expression);
}