// LuaExecutor.cpp
#include "LuaExecutor.h"

#include <iostream>

#include "EngineManager.h"
#include <sstream>

#include "PythonEngine.h"

LuaExecutor::LuaExecutor() {
    lua_.open_libraries(sol::lib::base);
    setupEnvironment();
}

void LuaExecutor::registerFunction(const std::string& name,
                                 const std::string& engine,
                                 const std::string& backendFunc) {
    funcRegistry_[name] = {engine, backendFunc};
}
//
// std::string luaValueToString(const sol::object& value) {
//     switch (value.get_type()) {
//         case sol::type::number: {
//             std::stringstream ss;
//             ss << value.as<double>();
//             return ss.str();
//         }
//         case sol::type::string:
//             return "\"" + value.as<std::string>() + "\"";
//         case sol::type::boolean:
//             return value.as<bool>() ? "True" : "False"; // Python 使用大写
//         case sol::type::table: {
//             sol::table table = value.as<sol::table>();
//             size_t size = table.size();
//
//             // 判断是否为空表
//             if (size == 0) {
//                 return "[]";
//             }
//
//             // 检查是否为数组（连续整数索引）
//             bool is_array = true;
//             for (size_t i = 1; i <= size; i++) {
//                 if (table[i] == sol::nil) {
//                     is_array = false;
//                     break;
//                 }
//             }
//
//             if (is_array) {
//                 // 处理数组表 - 转换为 Python 列表
//                 std::string result = "[";
//                 for (size_t i = 1; i <= size; i++) {
//                     if (i > 1) result += ", ";
//                     result += luaValueToString(table[i]);
//                 }
//                 result += "]";
//                 return result;
//             } else {
//                 // 处理字典表 - 转换为 Python 字典
//                 std::string result = "{";
//                 bool first = true;
//                 for (auto& pair : table) {
//                     if (!first) result += ", ";
//                     sol::object key = pair.first;
//                     sol::object val = pair.second;
//
//                     // 只支持字符串和数字键
//                     if (key.is<std::string>()) {
//                         result += "\"" + key.as<std::string>() + "\": ";
//                     } else if (key.is<double>()) {
//                         result += std::to_string(static_cast<int>(key.as<double>())) + ": ";
//                     } else {
//                         // 不支持的类型跳过
//                         continue;
//                     }
//
//                     result += luaValueToString(val);
//                     first = false;
//                 }
//                 result += "}";
//                 return result;
//             }
//         }
//         default:
//             return "\"<unsupported type>\"";
//     }
// }


// 辅助函数：转义字符串
std::string escapeString(const std::string& input) {
    std::ostringstream escaped;
    for (char c : input) {
        switch (c) {
            case '\\': escaped << "\\\\"; break;
            case '\"': escaped << "\\\""; break;
            case '\n': escaped << "\\n"; break;
            case '\r': escaped << "\\r"; break;
            case '\t': escaped << "\\t"; break;
            default: escaped << c;
        }
    }
    return escaped.str();
}
std::string luaValueToString(const sol::object& value) {
    switch (value.get_type()) {
        case sol::type::number: {
            std::stringstream ss;
            double num = value.as<double>();
            // 检查是否为整数
            if (num == static_cast<int>(num)) {
                ss << static_cast<int>(num);
            } else {
                ss << std::fixed << std::setprecision(6) << num;
                // 移除多余的零和小数点
                std::string s = ss.str();
                if (s.find('.') != std::string::npos) {
                    // 移除末尾的零
                    s.erase(s.find_last_not_of('0') + 1, std::string::npos);
                    // 如果只剩下小数点，也移除
                    if (s.back() == '.') s.pop_back();
                }
                return s;
            }
            return ss.str();
        }
        case sol::type::string:
            return "\"" + escapeString(value.as<std::string>()) + "\"";
        case sol::type::boolean:
            return value.as<bool>() ? "True" : "False";
        case sol::type::table: {
            sol::table table = value.as<sol::table>();
            size_t size = table.size();

            // 检查是否为多维数组（矩阵）
            bool is_matrix = (size > 0);
            if (is_matrix) {
                // 检查第一元素是否为表
                sol::object first = table[1];
                if (first.get_type() == sol::type::table) {
                    sol::table inner = first.as<sol::table>();
                    size_t inner_size = inner.size();

                    // 检查内部元素是否都是数字
                    for (size_t j = 1; j <= inner_size; j++) {
                        sol::object inner_obj = inner[j];
                        if (inner_obj.get_type() != sol::type::number) {
                            is_matrix = false;
                            break;
                        }
                    }
                } else {
                    is_matrix = false;
                }
            }

            if (is_matrix) {
                // 处理矩阵 - 转换为 Python 列表的列表
                std::string result = "[";
                for (size_t i = 1; i <= size; i++) {
                    if (i > 1) result += ", ";
                    sol::table inner = table[i].get<sol::table>();
                    result += "[";
                    for (size_t j = 1; j <= inner.size(); j++) {
                        if (j > 1) result += ", ";
                        result += luaValueToString(inner[j]);
                    }
                    result += "]";
                }
                result += "]";
                return result;
            } else if (size > 0) {
                // 检查是否为数组（连续整数索引）
                bool is_array = true;
                for (size_t i = 1; i <= size; i++) {
                    sol::object item = table[i];
                    if (item == sol::nil || item.get_type() == sol::type::lua_nil) {
                        is_array = false;
                        break;
                    }
                }

                if (is_array) {
                    // 处理一维数组
                    std::string result = "[";
                    for (size_t i = 1; i <= size; i++) {
                        if (i > 1) result += ", ";
                        result += luaValueToString(table[i]);
                    }
                    result += "]";
                    return result;
                }
            }

            // 默认处理为字典
            std::string result = "{";
            bool first = true;
            for (auto& pair : table) {
                sol::object key = pair.first;
                sol::object val = pair.second;

                // 检查键的类型
                sol::type key_type = key.get_type();

                // 只处理字符串和数字键
                if (key_type == sol::type::string || key_type == sol::type::number) {
                    if (!first) result += ", ";

                    if (key_type == sol::type::string) {
                        result += "\"" + escapeString(key.as<std::string>()) + "\": ";
                    } else {
                        result += luaValueToString(key) + ": ";
                    }

                    result += luaValueToString(val);
                    first = false;
                }
            }
            result += "}";
            return result;
        }
        default:
            return "None";  // Python 中的 None
    }
}

void LuaExecutor::setupEnvironment() {
    // 注册通用调用处理器
    lua_.set_function("__call_engine", [this](
        const std::string& funcName,
        sol::variadic_args va) {
        // 收集参数
        std::vector<std::string> args;
        for (const auto& arg : va) {
            sol::object obj = arg;
            args.push_back(luaValueToString(obj));
        }

        // 翻译为引擎调用
        return translateToEngineCall(funcName, args);
    });

    // 重载运算符调用
    lua_.script(R"(
        local function call_handler(func_name, ...)
            return __call_engine(func_name, ...)
        end

        -- 设置全局调用环境
        setmetatable(_G, {
            __index = function(_, name)
                return function(...)
                    return call_handler(name, ...)
                end
            end
        })
    )");
}

std::string LuaExecutor::translateToEngineCall(
    const std::string& funcName,
    const std::vector<std::string>& args) {

    auto it = funcRegistry_.find(funcName);
    if (it == funcRegistry_.end()) {
        return "Error: Function '" + funcName + "' not registered";
    }

    const auto& [engine, backendFunc] = it->second;

    auto enginePtr = EngineManager::instance().getEngine(engine);
    if (!enginePtr) {
        return "Error: " + engine + " engine not available";
    }
    if (engine == "python") {
        // 解析模块和函数名
        size_t pos = backendFunc.find_last_of('.');
        if (pos == std::string::npos) {
            return "Error: Invalid backend function format for Python";
        }

        std::string module = backendFunc.substr(0, pos);
        std::string function = backendFunc.substr(pos + 1);

        // 尝试转换为Python引擎
        if (auto pythonEngine = dynamic_cast<PythonEngine*>(enginePtr)) {
            return pythonEngine->executeAlgorithm(function, args);
        }
    }

    std::string script;

    if (engine == "python") {
        script = "__engine_result = " + backendFunc + "(";
        for (size_t i = 0; i < args.size(); ++i) {
            if (i > 0) script += ", ";
            script += args[i];
        }
        script += ")";
    }
    else if (engine == "matlab") {
        script = "result = " + backendFunc + "(";
        for (size_t i = 0; i < args.size(); ++i) {
            if (i > 0) script += ", ";
            script += args[i];
        }
        script += "); __engine_result = num2str(result);";
    }



    return enginePtr->execute(script);
}

std::string LuaExecutor::execute(const std::string& expression) {
    try {
        // 创建安全的执行环境
        sol::state_view lua(lua_);

        // 加载并编译表达式
        sol::load_result lr = lua.load("return " + expression);
        if (!lr.valid()) {
            sol::error err = lr;
            return "加载错误: " + std::string(err.what());
        }

        // 获取函数并调用
        sol::protected_function pf = lr;
        sol::protected_function_result pfr = pf();

        // 检查执行结果
        if (!pfr.valid()) {
            sol::error err = pfr;
            return "执行错误: " + std::string(err.what());
        }

        // 安全提取结果对象
        sol::object result_obj = pfr.get<sol::object>();
        return luaValueToString(result_obj);
    } catch (const std::exception& e) {
        return "异常: " + std::string(e.what());
    }
}