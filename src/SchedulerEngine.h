//
// Created by ma<PERSON><PERSON> on 2025/8/6.
//

#ifndef SCHEDULERENGINE_H
#define SCHEDULERENGINE_H


// SchedulerEngine.h
#include "LuaExecutor.h"
#include <string>

class SchedulerEngine {
public:
    SchedulerEngine();

    void loadConfiguration(const std::string& configPath);

    void registerAlgorithm(const std::string& name,
                         const std::string& engine,
                         const std::string& module,
                         const std::string& function);

    std::string compute(const std::string& expression);

private:
    LuaExecutor executor_;
};
#endif //SCHEDULERENGINE_H
