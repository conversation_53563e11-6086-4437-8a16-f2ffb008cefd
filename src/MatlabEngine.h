//
// Created by <PERSON><PERSON><PERSON> on 2025/8/6.
//

#ifndef MATLABENGINE_H
#define MATLABENGINE_H


#include "EngineBase.h"
#include <engine.h>
#include <string>
#include <stdexcept>

class MatlabEngine : public EngineBase {
public:
    MatlabEngine();
    ~MatlabEngine() override;

    bool initialize() override;
    std::string execute(const std::string& script) override;

private:
    Engine* ep_;
};

#endif //MATLABENGINE_H
