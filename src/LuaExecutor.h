//
// Created by ma<PERSON><PERSON> on 2025/8/6.
//

#ifndef LUAEXECUTOR_H
#define LUAEXECUTOR_H


#include <sol/sol.hpp>
#include <string>
#include <unordered_map>

class LuaExecutor {
public:
    LuaExecutor();
    std::string execute(const std::string& expression);

    void registerFunction(const std::string& name,
                        const std::string& engine,
                        const std::string& backendFunc);

private:
    sol::state lua_;
    std::unordered_map<std::string, std::pair<std::string, std::string>> funcRegistry_;

    void setupEnvironment();
    std::string translateToEngineCall(const std::string& funcName,
                                    const std::vector<std::string>& args);
};

#endif //LUAEXECUTOR_H
