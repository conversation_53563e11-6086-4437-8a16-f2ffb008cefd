//
// Created by ma<PERSON><PERSON> on 2025/8/6.
//

#include "MatlabEngine.h"

#include <iostream>


MatlabEngine::MatlabEngine() : ep_(nullptr) {
    if (!(ep_ = engOpen(""))) {
        throw std::runtime_error("Failed to open MATLAB engine");
    }
    engOutputBuffer(ep_, NULL, 0); // Initialize buffer
}

MatlabEngine::~MatlabEngine() {
    if (ep_) {
        engClose(ep_);
    }
}

bool MatlabEngine::initialize() {
    return (ep_ != nullptr);
}

std::string MatlabEngine::execute(const std::string& script) {
    if (!ep_) {
        return "MATLAB engine not initialized";
    }
    std::cout << __LINE__ << " " << __FUNCDNAME__ << std::endl;
    const int buffer_size = 4096;
    char buffer[buffer_size] = {0};
    engOutputBuffer(ep_, buffer, buffer_size-1);

    int status = engEvalString(ep_, script.c_str());
    if (status != 0) {
        return "MATLAB execution error (status=" + std::to_string(status) + ")";
    }

    // Ensure null-termination
    buffer[buffer_size-1] = '\0';
    return std::string(buffer);
}