//
// Created by <PERSON><PERSON><PERSON> on 2025/8/6.
//

#ifndef PYTHONENGINE_H
#define PYTHONENGINE_H


#include <filesystem>

#include "EngineBase.h"
#include <string>
#include <mutex>


// 算法信息结构体
struct AlgorithmInfo {
    std::string engine;
    std::string module;
    std::string function;
    std::vector<std::string> dependencies;
};

class PythonEngine : public EngineBase {
public:
    PythonEngine();
    ~PythonEngine() override;

    bool initialize() override;
    std::string execute(const std::string& script) override;

    void loadConfiguration(const std::string& configPath);

    std::string executeAlgorithm(const std::string& algoName,
                              const std::vector<std::string>& args);

private:
    void addPythonPath(const std::string& path);
    void ensureDependenciesLoaded(const AlgorithmInfo& algoInfo);
private:
    std::mutex mutex_;
    bool is_initialized_;
    // 存储额外的Python路径
    std::vector<std::filesystem::path> additionalPaths_;
    std::filesystem::path workingDirectory_{"D:\\source\\qml-lua\\dynamic_algo\\cmake-build-debug"};

    // 算法注册表：算法名 -> 算法信息
    std::unordered_map<std::string, AlgorithmInfo> algorithmRegistry_;
};


#endif //PYTHONENGINE_H
