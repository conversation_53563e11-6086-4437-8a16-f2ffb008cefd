% 信号处理
function [freq, amp] = fft_analysis(signal, fs)
    L = length(signal);
    Y = fft(signal);
    P2 = abs(Y/L);
    P1 = P2(1:L/2+1);
    freq = fs*(0:(L/2))/L;
    amp = P1;
end

% 控制系统
function [y, t] = step_response(sys)
    [y, t] = step(sys);
end

% 图像处理
function output = image_filter(input, filter_type)
    if strcmp(filter_type, 'gaussian')
        output = imgaussfilt(input, 2);
    elseif strcmp(filter_type, 'median')
        output = medfilt2(input);
    end
end