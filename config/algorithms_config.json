{"python_paths": ["algorithms"], "algorithms": [{"name": "matrix_multiply", "engine": "python", "module": "algorithms", "function": "matrix_multiply", "dependencies": ["joblib", "numpy"]}, {"name": "predict_linear_model", "engine": "python", "module": "algorithms", "function": "predict_linear_model", "dependencies": ["joblib", "numpy"]}, {"name": "integral", "engine": "python", "module": "algorithms", "function": "integral", "dependencies": ["joblib", "numpy"]}]}