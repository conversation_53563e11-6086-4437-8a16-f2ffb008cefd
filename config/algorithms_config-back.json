{"algorithms": [{"name": "python_sin", "language": "python", "module": "math", "function": "sin", "input_params": [{"name": "x", "type": "double"}], "output_type": "double", "timeout_ms": 1000}, {"name": "cpp_add", "language": "cpp", "library_path": "./add.cp313-win_amd64.dll", "function": "PyInit_add", "input_params": [{"name": "a", "type": "int"}, {"name": "b", "type": "int"}], "output_type": "int", "dynamic_load": true}]}