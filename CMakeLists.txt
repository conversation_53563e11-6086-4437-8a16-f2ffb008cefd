cmake_minimum_required(VERSION 3.31)
project(dynamic_algo )

set(CMAKE_CXX_STANDARD 17)

set(CMAKE_PREFIX_PATH ${CMAKE_SOURCE_DIR}/external/lua_JIT-2.0.5/cmake ${CMAKE_SOURCE_DIR}/external/python)

set(CMAKE_VERBOSE_MAKEFILE ON)

# 查找Lua库
find_package(LuaJIT   2.0.5 REQUIRED CONFIG)
find_package(Python 3 REQUIRED CONFIG)

find_path(MATLAB_INCLUDE_DIR engine.h
        PATHS "E:/Program Files/MATLAB/R2024b/extern/include"
        DOC "MATLAB engine include directory")
find_library(MATLAB_ENGINE_LIB eng
        PATHS "E:/Program Files/MATLAB/R2024b/extern/lib/win64/microsoft"
        DOC "MATLAB engine library")
find_library(MATLAB_MX_LIB mx
        PATHS "E:/Program Files/MATLAB/R2024b/extern/lib/win64/microsoft"
        DOC "MATLAB mx library")

if (MATLAB_INCLUDE_DIR AND MATLAB_ENGINE_LIB AND MATLAB_MX_LIB)
    set(MATLAB_LIBRARIES ${MATLAB_ENGINE_LIB} ${MATLAB_MX_LIB})
endif()


find_path(SOL2_INCLUDE_DIR sol/sol.hpp
        PATHS /usr/include /usr/local/include ${CMAKE_SOURCE_DIR}/external/sol2/include)

if (SOL2_INCLUDE_DIR)
    include_directories(${SOL2_INCLUDE_DIR})
else()
    message(FATAL_ERROR "Sol2 not found")
endif()

# 生成可执行文件
add_executable(dynamic_algo
        src/EngineBase.h
        src/EngineManager.h
        src/EngineManager.cpp
        src/LuaExecutor.cpp
        src/LuaExecutor.h
        src/MatlabEngine.cpp
        src/MatlabEngine.h
        src/PythonEngine.cpp
        src/PythonEngine.h
        src/SchedulerEngine.cpp
        src/SchedulerEngine.h
        src/main.cpp
)

target_include_directories(dynamic_algo
    PRIVATE
        ${CMAKE_SOURCE_DIR}/external/
        ${CMAKE_SOURCE_DIR}/external/json
        ${MATLAB_INCLUDE_DIR}
)

target_link_libraries(dynamic_algo
        PRIVATE
        LuaJIT::LuaJIT
        Python::Python
        ${MATLAB_LIBRARIES}
)

# 复制配置文件到编译目录
configure_file(config/algorithms_config.json ${CMAKE_BINARY_DIR}/config/algorithms_config.json COPYONLY)