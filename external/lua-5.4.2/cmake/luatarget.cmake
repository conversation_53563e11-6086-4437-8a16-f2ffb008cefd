# luatarget.cmake - Lua 导入目标定义
# 定义 Lua::Lua 目标，供用户项目通过 target_link_libraries 使用

if(TARGET Lua::Lua)
    return()
endif()

# 检查依赖项是否已设置（需先加载 LuaConfig.cmake 或 FindLua.cmake）
if(NOT Lua_INCLUDE_DIRS OR NOT Lua_LIBRARIES)
    message(FATAL_ERROR "Lua 配置未找到，请先加载 luaconfig.cmake 或运行 find_package(Lua)")
endif()

# 定义接口导入目标
add_library(Lua::Lua INTERFACE IMPORTED)
set_target_properties(Lua::Lua PROPERTIES
        # 头文件路径
        INTERFACE_INCLUDE_DIRECTORIES "${Lua_INCLUDE_DIRS}"
        # 链接库（支持静态/动态库，根据 Lua_LIBRARIES 自动适配）
        INTERFACE_LINK_LIBRARIES "${Lua_LIBRARIES}"
        # 编译宏定义（如 LUA_COMPAT_ALL 等兼容性选项）
        INTERFACE_COMPILE_DEFINITIONS "${Lua_COMPILE_DEFINITIONS}"
        # C 编译选项（如 -std=c99 等）
        INTERFACE_COMPILE_OPTIONS "${Lua_C_FLAGS}"
)

message(STATUS "已导入 Lua 目标: Lua::Lua")