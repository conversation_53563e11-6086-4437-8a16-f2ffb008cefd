# LuaConfigVersion.cmake（手动编写，适配 Lua 5.4.2）
set(PACKAGE_VERSION "5.4.2")

# 版本号拆解（主版本.次版本.修订版本）
set(PACKAGE_VERSION_MAJOR 5)
set(PACKAGE_VERSION_MINOR 4)
set(PACKAGE_VERSION_PATCH 2)

# 版本兼容策略：SameMinorVersion（主版本相同，次版本 ≥ 要求）
if(PACKAGE_VERSION_MAJOR VERSION_EQUAL PACKAGE_FIND_VERSION_MAJOR)
    if(PACKAGE_VERSION_MINOR VERSION_GREATER_EQUAL PACKAGE_FIND_VERSION_MINOR)
        # 次版本号 ≥ 要求 → 兼容
        set(PACKAGE_VERSION_COMPATIBLE 1)
        if(PACKAGE_VERSION_MINOR VERSION_EQUAL PACKAGE_FIND_VERSION_MINOR AND
                PACKAGE_VERSION_PATCH VERSION_GREATER_EQUAL PACKAGE_FIND_VERSION_PATCH)
            # 修订版本号 ≥ 要求 → 完全匹配
            set(PACKAGE_VERSION_EXACT 1)
        endif()
    endif()
endif()