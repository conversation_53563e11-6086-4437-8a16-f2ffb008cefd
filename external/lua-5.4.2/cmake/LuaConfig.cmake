# LuaConfig.cmake - Lua 配置导出文件
# 导出 Lua 库的版本、包含目录、链接库及编译选项

# 版本信息（需与实际安装的 Lua 版本匹配）
set(Lua_VERSION "5.4.2")
set(Lua_VERSION_MAJOR 5)
set(Lua_VERSION_MINOR 4)
set(Lua_VERSION_PATCH 2)

# 包含目录（用户项目需包含的头文件路径）

set(Lua_INCLUDE_DIRS "${CMAKE_CURRENT_LIST_DIR}/../include")

# 链接库（用户项目需链接的库文件）
set(Lua_LIBRARIES "${CMAKE_CURRENT_LIST_DIR}/../lib/lua54.lib")

# 编译选项（可选，如 Lua 依赖的 C 标准或宏定义）
#set(Lua_COMPILE_DEFINITIONS "@LUA_COMPILE_DEFINITIONS@")
#set(Lua_C_FLAGS "@LUA_C_FLAGS@")

# 标记为已找到
set(Lua_FOUND TRUE)

# 提供导入目标（现代 CMake 推荐用法）
if(NOT TARGET Lua::Lua)
    add_library(Lua::Lua INTERFACE IMPORTED)
    set_target_properties(Lua::Lua PROPERTIES
            INTERFACE_INCLUDE_DIRECTORIES "${Lua_INCLUDE_DIRS}"
            INTERFACE_LINK_LIBRARIES "${Lua_LIBRARIES}"
            INTERFACE_COMPILE_DEFINITIONS "${Lua_COMPILE_DEFINITIONS}"
            INTERFACE_COMPILE_OPTIONS "${Lua_C_FLAGS}"
    )
endif()