# Python 库目标定义

# 导入目标（根据实际路径修改）
if(NOT TARGET Python::Python)
    add_library(Python::Python SHARED IMPORTED)

    find_library(PYTHON_LIBRARY_DEBUG
            NAMES python313_d
            PATHS
            "${CMAKE_CURRENT_LIST_DIR}/amd64/"
            "C:/Program Files/LuaJIT/lib"
            DOC "Python debug library"
    )
    message(STATUS  -+ ${CMAKE_CURRENT_LIST_DIR}/../amd64/)
    find_library(PYTHON_LIBRARY_RELEASE
            NAMES python313
            PATHS
            "${CMAKE_CURRENT_LIST_DIR}/amd64/"
            "C:/Program Files/LuaJIT/lib"
            DOC "Python release library"
    )

    # 设置目标属性（修改为实际路径）
    set_target_properties(Python::Python PROPERTIES
            INTERFACE_INCLUDE_DIRECTORIES "D:/source/qml-lua/dynamic_algo/external/python/include"
            IMPORTED_LOCATION_DEBUG "${PYTHON_LIBRARY_DEBUG}"
            IMPORTED_LOCATION_RELEASE "${PYTHON_LIBRARY_RELEASE}"
            IMPORTED_IMPLIB_DEBUG "${PYTHON_LIBRARY_DEBUG}"
            IMPORTED_IMPLIB_RELEASE "${PYTHON_LIBRARY_RELEASE}"
            INTERFACE_COMPILE_OPTIONS ""


    )

    # 添加依赖项（可选）
    if(UNIX AND NOT APPLE)
        set_target_properties(Python::Python PROPERTIES
                INTERFACE_LINK_LIBRARIES "pthread;dl;util"
        )
    endif()
endif()

# 可选：添加 Python 解释器目标
if(NOT TARGET Python::Interpreter)
    add_executable(Python::Interpreter IMPORTED)
    set_property(TARGET Python::Interpreter PROPERTY
            IMPORTED_LOCATION "D:/source/qml-lua/dynamic_algo/external/python/amd64/python3.13.exe"  # 修改为实际解释器路径
    )
endif()