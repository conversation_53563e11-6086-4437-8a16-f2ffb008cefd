# Python 版本兼容性检查
set(PACKAGE_VERSION 3.13.10)  # 修改为实际 Python 版本

# 检查 CMake 版本兼容性
if(PACKAGE_VERSION VERSION_LESS 3.0)
    set(PACKAGE_VERSION_COMPATIBLE FALSE)
else()
    # 检查主版本是否匹配
    if("3" STREQUAL "${PACKAGE_FIND_VERSION_MAJOR}")
        set(PACKAGE_VERSION_COMPATIBLE TRUE)

        # 检查是否满足精确版本要求
        if(PACKAGE_FIND_VERSION_COUNT GREATER 1 AND
                PACKAGE_VERSION VERSION_LESS PACKAGE_FIND_VERSION)
            set(PACKAGE_VERSION_EXACT FALSE)
        else()
            set(PACKAGE_VERSION_EXACT TRUE)
        endif()
    else()
        set(PACKAGE_VERSION_COMPATIBLE FALSE)
    endif()
endif()


if(NOT PACKAGE_VERSION_COMPATIBLE)
    message(STATUS "Incompatible Python version: found ${PACKAGE_VERSION}, required ${PACKAGE_FIND_VERSION}")
endif()
