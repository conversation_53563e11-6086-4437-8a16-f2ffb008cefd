# Python 开发库配置文件
include(CMakeFindDependencyMacro)

# 包含版本检查
include("${CMAKE_CURRENT_LIST_DIR}/PythonConfigVersion.cmake")

# 包含目标定义
if(NOT TARGET Python::Python)
    include("${CMAKE_CURRENT_LIST_DIR}/PythonTargets.cmake")
endif()

# 设置公共变量
set(Python_VERSION 3.13.5)  # 修改为实际 Python 版本
set(Python_INCLUDE_DIRS "C:/Program Files/WindowsApps/PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0/include")  # 修改为实际头文件路径
set(Python_LIBRARIES Python::Python)

# 向后兼容变量
set(PYTHON_FOUND TRUE)
set(PYTHON_VERSION_STRING ${Python_VERSION})
set(PYTHON_INCLUDE_DIR ${Python_INCLUDE_DIRS})
set(PYTHON_LIBRARY Python::Python)

# 提供 find_package 组件支持
include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(Python
        REQUIRED_VARS Python_INCLUDE_DIRS Python_LIBRARIES
        VERSION_VAR Python_VERSION
)