# 定义 LuaJIT 目标和路径变量
include(CMakeFindDependencyMacro)

# 查找 LuaJIT 头文件
find_path(LUAJIT_INCLUDE_DIR
        NAMES lua.h luajit.h
        PATHS
        "${CMAKE_CURRENT_LIST_DIR}/../include"  # 相对路径（根据安装位置调整）
        "/usr/include/luajit-2.1"              # Linux 默认路径
        "/usr/local/include/luajit-2.1"        # macOS 默认路径
        "C:/Program Files/LuaJIT/include"      # Windows 默认路径
        DOC "LuaJIT include directory"
)

# 查找 LuaJIT 库文件（区分平台和配置）
if(WIN32)
    # Windows: 静态库（.lib）或动态库（.dll）
    find_library(LUAJIT_LIBRARY_DEBUG
            NAMES lua51d
            PATHS
            "${CMAKE_CURRENT_LIST_DIR}/../lib/"
            "C:/Program Files/LuaJIT/lib"
            DOC "LuaJIT debug library"
    )
    find_library(LUAJIT_LIBRARY_RELEASE
            NAMES lua51
            PATHS
            "${CMAKE_CURRENT_LIST_DIR}/../lib/"
            "C:/Program Files/LuaJIT/lib"
            DOC "LuaJIT release library"
    )
else()
    # Linux/macOS: 动态库（.so/.dylib）或静态库（.a）
    find_library(LUAJIT_LIBRARY
            NAMES luajit-5.1 luajit
            PATHS
            "${CMAKE_CURRENT_LIST_DIR}/../lib"
            "/usr/lib/x86_64-linux-gnu"    # Linux 默认路径
            "/usr/local/lib"               # macOS 默认路径
            DOC "LuaJIT library"
    )
endif()

# 合并 debug/release 库（CMake 3.1+ 支持）
include(SelectLibraryConfigurations)
select_library_configurations(LUAJIT)

# 定义导入目标（核心）
if(NOT TARGET LuaJIT::LuaJIT)
    include("${CMAKE_CURRENT_LIST_DIR}/luajittarget.cmake")
endif()

# 设置标准变量（兼容 find_package 接口）
set(LUAJIT_FOUND TRUE)
set(LUAJIT_INCLUDE_DIRS ${LUAJIT_INCLUDE_DIR})
set(LUAJIT_LIBRARIES LuaJIT::LuaJIT)
set(LUAJIT_VERSION "2.0.5")

# 打印配置信息
message(STATUS "Found LuaJIT: ${LUAJIT_LIBRARY} (version ${LUAJIT_VERSION})")