# 版本兼容性声明
set(PACKAGE_VERSION "2.0.5")

# 检查版本兼容性（支持 >= 2.0.5 的版本）
if(PACKAGE_VERSION VERSION_LESS PACKAGE_FIND_VERSION)
    set(PACKAGE_VERSION_COMPATIBLE FALSE)
else()
    if("2.0.5" MATCHES "^([0-9]+)\\.")
        set(MAJOR_VERSION ${CMAKE_MATCH_1})
    else()
        set(MAJOR_VERSION 2.0.5)
    endif()

    if(PACKAGE_FIND_VERSION_MAJOR STREQUAL MAJOR_VERSION)
        set(PACKAGE_VERSION_COMPATIBLE TRUE)
    else()
        set(PACKAGE_VERSION_COMPATIBLE FALSE)
    endif()

    if(PACKAGE_VERSION STREQUAL PACKAGE_FIND_VERSION)
        set(PACKAGE_VERSION_EXACT TRUE)
    endif()
endif()