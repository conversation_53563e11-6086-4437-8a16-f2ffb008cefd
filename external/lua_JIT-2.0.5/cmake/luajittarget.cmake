# 定义 LuaJIT 导入目标（静态库/动态库通用）
add_library(LuaJIT::LuaJIT SHARED IMPORTED)

# 设置接口包含目录（必须与 LuaJIT 头文件路径匹配）
set_target_properties(LuaJIT::LuaJIT PROPERTIES
        INTERFACE_INCLUDE_DIRECTORIES "${LUAJIT_INCLUDE_DIR}"
)

# 根据平台设置库文件路径
if(WIN32)
    # Windows: 区分 Debug/Release 配置
    if(LUAJIT_LIBRARY_DEBUG AND LUAJIT_LIBRARY_RELEASE)
        set_target_properties(LuaJIT::LuaJIT PROPERTIES
                IMPORTED_LOCATION_DEBUG "${LUAJIT_LIBRARY_DEBUG}"
                IMPORTED_LOCATION_RELEASE "${LUAJIT_LIBRARY_RELEASE}"
                IMPORTED_IMPLIB_DEBUG "${LUAJIT_LIBRARY_DEBUG}"
                IMPORTED_IMPLIB_RELEASE "${LUAJIT_LIBRARY_RELEASE}"
        )
    elseif(LUAJIT_LIBRARY_RELEASE)
        set_target_properties(LuaJIT::LuaJIT PROPERTIES
                IMPORTED_LOCATION "${LUAJIT_LIBRARY_RELEASE}"
                IMPORTED_IMPLIB "${LUAJIT_LIBRARY_RELEASE}"
        )
    endif()
elseif(APPLE)
    # macOS: 动态库（.dylib）
    set_target_properties(LuaJIT::LuaJIT PROPERTIES
            IMPORTED_LOCATION "${LUAJIT_LIBRARY}"
            LINK_FLAGS "-pagezero_size 10000 -image_base 100000000"  # LuaJIT 特殊链接参数
    )
else()
    # Linux: 动态库（.so）
    set_target_properties(LuaJIT::LuaJIT PROPERTIES
            IMPORTED_LOCATION "${LUAJIT_LIBRARY}"
    )
endif()